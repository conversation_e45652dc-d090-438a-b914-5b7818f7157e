# 血量系统总结

## 🩸 血量机制

### 基础设置
- **初始血量**: 10滴血
- **最大血量**: 10滴血
- **扣血条件**: 蚊子存活超过时间限制

### 扣血规则
- **每次扣血**: 1滴血
- **第一关**: 蚊子存活20秒后扣血
- **第二关**: 蚊子存活15秒后扣血
- **死亡条件**: 血量降到0时游戏结束

## ⏱️ 时间计算

### 理论上的游戏时长
如果玩家完全不击打蚊子：

**第一关**:
- 10只蚊子 × 20秒 = 200秒 = 3分20秒
- 但血量只有10滴，所以最多10次扣血就结束

**第二关**:
- 15只蚊子 × 15秒 = 225秒 = 3分45秒
- 同样，血量只有10滴，最多10次扣血就结束

### 实际游戏时长
**最短游戏时长**（完全不击打）:
- **第一关**: 10次 × 20秒 = 200秒 = 3分20秒
- **第二关**: 10次 × 15秒 = 150秒 = 2分30秒

## 🎯 游戏策略

### 玩家目标
1. **击杀目标**: 第一关10只，第二关15只蚊子
2. **时间压力**: 必须在蚊子逃跑前击中它们
3. **血量管理**: 最多只能让10只蚊子逃跑

### 难度设计
- **第一关**: 相对宽松，20秒时间限制
- **第二关**: 更有挑战性，15秒时间限制
- **血量限制**: 为游戏增加紧迫感

## 🔄 游戏循环

### 单只蚊子的生命周期
1. **生成**: 蚊子在随机位置生成
2. **移动**: 蚊子水平移动 + Z轴移动（变大变小）
3. **计时**: 从生成开始计时
4. **结果**:
   - 被击中 → 击杀数+1，生成新蚊子
   - 超时 → 血量-1，3秒后生成新蚊子

### 游戏结束条件
1. **胜利**: 击杀足够数量的蚊子
2. **失败**: 血量耗尽（10只蚊子逃跑）
3. **失败**: 击打次数用完

## 📊 数据示例

### 完美游戏（无扣血）
- **第一关**: 击杀10只蚊子，血量保持10/10
- **第二关**: 击杀15只蚊子，血量保持10/10

### 边缘游戏（最大扣血）
- **第一关**: 击杀10只蚊子，9只逃跑，血量1/10
- **第二关**: 击杀15只蚊子，9只逃跑，血量1/10

### 失败游戏（血量耗尽）
- 任何关卡：10只蚊子逃跑，血量0/10，游戏结束

## 🎮 平衡性分析

### 时间压力
- **第一关**: 20秒相对宽松，适合新手
- **第二关**: 15秒增加挑战，需要更快反应

### 容错空间
- **10滴血**: 提供足够的容错空间
- **不会太严苛**: 允许一些失误
- **不会太宽松**: 仍有挑战性

### 策略深度
- **风险管理**: 玩家需要权衡击打准确性和速度
- **时间管理**: 必须在时间限制内做出决策
- **资源管理**: 血量和击打次数都是有限资源

## ✅ 验证要点

### 血量系统测试
- [ ] 初始血量显示10/10
- [ ] 蚊子超时后血量正确减少
- [ ] 血量显示实时更新
- [ ] 血量为0时游戏结束

### 时间系统测试
- [ ] 第一关蚊子20秒后准确扣血
- [ ] 第二关蚊子15秒后准确扣血
- [ ] 时间计算准确无误

### 游戏流程测试
- [ ] 10次扣血后游戏结束
- [ ] 击中蚊子不扣血
- [ ] 血量耗尽显示正确的游戏结束信息

这个血量系统设计合理，为游戏提供了适当的挑战性和策略深度！
