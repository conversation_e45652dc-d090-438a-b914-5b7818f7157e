// app.js
App({
  globalData: {
    // 游戏全局数据
    currentLevel: 1,
    totalLevels: 2,
    gameConfig: {
      level1: {
        brightness: 0.6,        // 屏幕亮度 (0-1) - 第一关不那么黑
        mosquitoSpeed: 1,       // 蚊子水平移动速度
        mosquitoZSpeed: 0.008,  // 蚊子朝镜头移动速度
        mosquitoCount: 10,      // 需要击打的蚊子数量
        maxAttempts: 20,        // 最大击打次数
        hitRange: 50,           // 击打范围
        maxHealth: 10,          // 最大血量
        mosquitoMaxScale: 1.5,  // 蚊子最大缩放（扣血阈值）- 调整为更合理的大小
        backgroundImage: '/images/level1_bg.jpg' // 第一关背景图片
      },
      level2: {
        brightness: 0.05,       // 屏幕亮度 - 第二关很黑
        mosquitoSpeed: 3,       // 蚊子水平移动速度较快
        mosquitoZSpeed: 0.012,  // 蚊子朝镜头移动速度较快
        mosquitoCount: 15,      // 需要击打的蚊子数量
        maxAttempts: 40,        // 最大击打次数
        hitRange: 50,           // 击打范围
        maxHealth: 10,          // 最大血量
        mosquitoMaxScale: 1.5,  // 蚊子最大缩放（扣血阈值）- 调整为更合理的大小
        backgroundImage: '/images/level2_bg.jpg' // 第二关背景图片
      }
    },
    // 广告相关
    adConfig: {
      hitRangeBonus: 20,        // 增加击打范围
      brightnessBonus: 0.2,     // 增加亮度
      attemptsBonus: 10,        // 增加击打次数
      reviveAttempts: 20        // 复活赠送次数
    }
  },

  onLaunch() {
    // 小程序启动时执行
    console.log('打蚊子游戏启动');
    
    // 检查更新
    this.checkForUpdate();
    
    // 初始化音频上下文
    this.initAudioContext();
  },

  onShow() {
    // 小程序显示时执行
    console.log('小程序显示');
  },

  onHide() {
    // 小程序隐藏时执行
    console.log('小程序隐藏');
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        console.log('新版本下载失败');
      });
    }
  },

  // 初始化音频上下文
  initAudioContext() {
    this.audioContext = wx.createInnerAudioContext();
    this.audioContext.autoplay = false;
    this.audioContext.loop = false;
  },

  // 获取当前关卡配置
  getCurrentLevelConfig() {
    return this.globalData.gameConfig[`level${this.globalData.currentLevel}`];
  },

  // 设置当前关卡
  setCurrentLevel(level) {
    this.globalData.currentLevel = level;
  },

  // 重置游戏数据
  resetGameData() {
    this.globalData.currentLevel = 1;
  }
});
