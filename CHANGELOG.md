# 更新日志

## v2.0.0 - 重大更新

### 🎮 新增核心游戏机制

#### 蚊子移动系统升级
- **朝镜头移动**: 蚊子现在会慢慢朝玩家移动（变大）
- **远近循环**: 蚊子到达最近距离后会反向移动到最远距离
- **速度差异**: 第一关移动慢，第二关移动快
- **扣血机制**: 蚊子超过一定大小时会扣除玩家1点血量

#### 血量系统
- **10点血量**: 玩家拥有10点生命值
- **血量显示**: 状态栏显示当前血量/最大血量
- **血量条**: 可视化血量条，颜色从绿到红
- **爱心显示**: 10个爱心图标显示血量状态
- **低血量警告**: 血量≤3时红色闪烁警告
- **死亡机制**: 血量为0时游戏结束

#### 长按控制系统重构
- **循环距离**: 长按时距离在远近之间循环（0→1→0→1...）
- **可视化指示**: 实时显示当前击打距离百分比
- **距离条**: 彩色进度条显示距离状态
- **3秒循环**: 每3秒完成一次远近循环

#### 3D击打系统
- **位置匹配**: 需要击中蚊子的屏幕位置
- **距离匹配**: 需要匹配蚊子的Z轴距离
- **容错机制**: Z轴距离容错范围±20%
- **详细反馈**: 显示具体失败原因（位置偏了/距离不对）

### 🎨 UI/UX 改进

#### HUD界面升级
- **扩展状态栏**: 增加血量显示
- **血量可视化**: 血量条 + 爱心图标
- **距离指示器**: 长按时显示距离信息
- **更好的布局**: 为新UI元素调整间距

#### 视觉效果
- **亮度调整**: 第一关亮度60%（不那么黑），第二关5%（很黑）
- **血量警告**: 低血量时红色闪烁动画
- **击打反馈**: 更详细的击中/未击中提示

### 🔧 技术改进

#### 游戏逻辑优化
- **状态检查**: 增加血量检查到游戏状态判定
- **数据重置**: 重置游戏时恢复满血量
- **音频优化**: 根据蚊子距离动态调整音量

#### 代码结构
- **模块化**: 分离不同功能模块
- **错误处理**: 改进广告系统错误处理
- **性能优化**: 优化动画和定时器管理

### 🎯 游戏平衡

#### 难度调整
- **第一关**: 更容易看到蚊子，移动较慢
- **第二关**: 几乎看不到蚊子，移动较快
- **血量威胁**: 增加时间压力，必须快速击杀蚊子

#### 反馈系统
- **即时反馈**: 被咬时震动 + 提示
- **视觉反馈**: 血量变化动画
- **音频反馈**: 保持原有音效系统

### 📱 兼容性

#### 设备适配
- **响应式设计**: 适配不同屏幕尺寸
- **性能优化**: 流畅的动画效果
- **触摸优化**: 改进触摸响应

### 🐛 修复问题

#### 广告系统
- **移除过时插件**: 解决插件加载错误
- **改进错误处理**: 更好的广告失败处理
- **开发环境支持**: 开发时显示模拟弹窗

#### 游戏逻辑
- **状态同步**: 修复状态不同步问题
- **内存管理**: 改进音频资源管理
- **定时器清理**: 防止内存泄漏

## 升级指南

### 从v1.0升级到v2.0
1. **备份存档**: 当前进度可能会重置
2. **更新资源**: 确保所有音频和图片资源就位
3. **测试功能**: 重点测试新的血量和距离系统
4. **调整配置**: 根据需要调整游戏难度参数

### 配置说明
新增配置项：
- `maxHealth`: 最大血量
- `mosquitoZSpeed`: 蚊子Z轴移动速度
- `mosquitoMaxScale`: 扣血阈值

## 已知问题

### 待优化
- [ ] 音频在某些设备上可能有延迟
- [ ] 长时间游戏可能出现性能下降
- [ ] 广告加载在网络不佳时可能失败

### 计划功能
- [ ] 更多关卡
- [ ] 道具系统
- [ ] 成就系统
- [ ] 排行榜

## 反馈

如果遇到问题或有建议，请通过以下方式反馈：
- 游戏内反馈系统
- 开发者邮箱
- GitHub Issues（如果开源）
