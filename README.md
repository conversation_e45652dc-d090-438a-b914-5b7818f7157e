# 打蚊子游戏 - 微信小程序

一个基于听声辨位的打蚊子游戏，玩家需要通过声音判断蚊子的位置并进行击打。

## 游戏特色

- 🎵 **听声辨位**: 根据蚊子声音的方向和音量判断位置
- 👆 **长按击打**: 长按时间决定击打距离，松开位置决定击打点
- 🌟 **关卡系统**: 两个关卡，难度递增
- 📱 **广告集成**: 四种广告功能提升游戏体验
- 🎮 **沉浸体验**: 动态亮度调节，营造真实氛围

## 游戏玩法

### 核心机制
- **听声辨位**: 根据蚊子声音判断位置和距离
- **长按控制**: 长按屏幕控制击打距离，距离会在远近之间循环
- **血量系统**: 玩家有10点血量，蚊子靠近会扣血
- **3D击打**: 需要同时匹配位置和距离才能击中

### 第一关
- 屏幕亮度: 60% (相对明亮，能看到蚊子)
- 蚊子移动: 缓慢水平移动 + 朝镜头移动
- 目标: 击打10只蚊子
- 机会: 20次
- 血量: 10点

### 第二关
- 屏幕亮度: 5% (几乎看不到蚊子)
- 蚊子移动: 快速水平移动 + 快速朝镜头移动
- 目标: 击打15只蚊子
- 机会: 40次
- 血量: 10点

### 广告功能
1. **扩大范围**: 增加击打范围 (+20px)
2. **提高亮度**: 增加屏幕亮度 (+20%)
3. **增加次数**: 增加击打次数 (+10次)
4. **复活功能**: 游戏结束后复活并赠送20次机会

## 所需资源文件

### 图片资源 (images/ 目录)

请将以下图片文件放置在 `images/` 目录下：

1. **mosquito.png** (60x60px)
   - 蚊子图片
   - 建议使用透明背景的PNG格式
   - 黑色或深色蚊子图案，便于在暗背景下显示

### 音频资源 (audio/ 目录)

请将以下音频文件放置在 `audio/` 目录下：

1. **mosquito.mp3**
   - 蚊子飞舞的声音
   - 建议时长: 2-5秒，可循环播放
   - 格式: MP3, 采样率: 44.1kHz
   - 音量适中，支持动态调节

2. **hit.mp3**
   - 击打成功的声音 (手掌拍击声)
   - 建议时长: 0.5-1秒
   - 格式: MP3, 采样率: 44.1kHz
   - 清脆的拍击音效

3. **miss.mp3**
   - 击打失败的声音
   - 建议时长: 0.5-1秒
   - 格式: MP3, 采样率: 44.1kHz
   - 可选择"嗖"的挥空声或其他失败音效

4. **level_complete.mp3** (可选)
   - 关卡完成音效
   - 建议时长: 2-3秒
   - 格式: MP3, 采样率: 44.1kHz
   - 胜利/成功的音效

5. **game_over.mp3** (可选)
   - 游戏结束音效
   - 建议时长: 2-3秒
   - 格式: MP3, 采样率: 44.1kHz
   - 失败/结束的音效

## 目录结构

```
wenzi/
├── app.js                 # 小程序入口文件
├── app.json              # 全局配置
├── app.wxss              # 全局样式
├── sitemap.json          # 站点地图
├── project.config.json   # 项目配置
├── README.md             # 说明文档
├── pages/                # 页面目录
│   ├── game/             # 游戏主页面
│   │   ├── game.js
│   │   ├── game.json
│   │   ├── game.wxml
│   │   └── game.wxss
│   └── loading/          # 关卡加载页面
│       ├── loading.js
│       ├── loading.json
│       ├── loading.wxml
│       └── loading.wxss
├── utils/                # 工具函数
│   ├── audio.js          # 音频管理工具
│   └── game.js           # 游戏工具函数
├── images/               # 图片资源 (需要创建)
│   └── mosquito.png      # 蚊子图片
└── audio/                # 音频资源 (需要创建)
    ├── mosquito.mp3      # 蚊子声音
    ├── hit.mp3           # 击打声音
    ├── miss.mp3          # 未击中声音
    ├── level_complete.mp3 # 关卡完成音效 (可选)
    └── game_over.mp3     # 游戏结束音效 (可选)
```

## 开发配置

### 1. 微信开发者工具
- 下载并安装微信开发者工具
- 导入项目目录
- 配置AppID (在project.config.json中修改)

### 2. 广告配置

#### 获取广告位ID
1. 登录微信公众平台 (mp.weixin.qq.com)
2. 进入小程序后台 -> 推广 -> 流量主
3. 开通流量主功能（需要满足条件）
4. 创建激励视频广告位
5. 获取广告位ID（格式：adunit-xxxxxxxxxxxxxxxx）

#### 配置广告位ID
在 `pages/game/game.js` 中找到以下代码并替换为你的广告位ID：
```javascript
adUnitId: 'adunit-xxxxxxxxxxxxxxxx' // 替换为你的广告位ID
```

#### 开发环境说明
- 在开发环境中，广告功能会显示模拟弹窗
- 真机预览时需要配置正确的广告位ID
- 发布后需要等待广告审核通过

### 3. 音频文件格式要求
- 格式: MP3
- 采样率: 44.1kHz 或 48kHz
- 比特率: 128kbps 或更高
- 文件大小: 建议每个文件不超过1MB

### 4. 图片文件要求
- 格式: PNG (支持透明背景)
- 尺寸: 60x60px (可根据需要调整)
- 文件大小: 建议不超过50KB

## 部署说明

1. 准备好所有资源文件
2. 在微信开发者工具中预览和调试
3. 配置正确的AppID和广告位ID
4. 提交审核并发布

## 注意事项

- 确保所有音频文件都能正常播放
- 测试在不同设备上的兼容性
- 广告功能需要在真机上测试
- 注意小程序包大小限制 (2MB)

## 技术特点

- 使用微信小程序原生开发
- 支持3D音频效果 (音量随距离变化)
- 响应式设计，适配不同屏幕尺寸
- 优化的触摸交互体验
- 流畅的动画效果

## 联系方式

如有问题或建议，请联系开发者。
