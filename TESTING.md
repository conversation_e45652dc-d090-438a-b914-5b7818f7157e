# 测试指南

## 快速测试步骤

### 1. 基础测试（无需资源文件）
即使没有图片和音频资源，你也可以测试游戏的基本功能：

1. 在微信开发者工具中导入项目
2. 点击"编译"按钮
3. 游戏应该能正常启动，显示游戏界面
4. 测试触摸交互（长按和松开）
5. 测试广告按钮（会显示模拟弹窗）

### 2. 功能测试清单

#### 游戏主界面
- [ ] 状态栏显示正常（关卡、击杀数、剩余次数）
- [ ] 蚊子图片位置显示（即使是占位符）
- [ ] 长按屏幕显示指示器
- [ ] 松开手指显示击打效果
- [ ] 底部广告按钮显示正常

#### 交互测试
- [ ] 长按屏幕：指示器出现并放大
- [ ] 松开手指：显示击打结果（击中/未击中）
- [ ] 击打次数正确减少
- [ ] 击杀数量正确增加（当击中时）

#### 广告功能测试
- [ ] 点击"扩大范围"按钮：显示广告弹窗
- [ ] 点击"提高亮度"按钮：显示广告弹窗
- [ ] 点击"增加次数"按钮：显示广告弹窗
- [ ] 确认观看广告后：相应数值增加

#### 关卡系统测试
- [ ] 击杀足够蚊子后：显示关卡完成弹窗
- [ ] 点击"下一关"：跳转到加载页面
- [ ] 加载页面：显示进度条和动画效果
- [ ] 点击"开始挑战"：返回游戏页面

#### 游戏结束测试
- [ ] 用完所有次数：显示游戏结束弹窗
- [ ] 显示复活选项
- [ ] 点击"重新开始"：重置游戏

### 3. 常见问题排查

#### 问题：页面显示空白
**解决方案：**
- 检查控制台是否有错误信息
- 确认 app.json 中的页面路径正确
- 检查 pages 目录下的文件是否完整

#### 问题：音频无法播放
**解决方案：**
- 确认音频文件存在于 audio/ 目录
- 检查音频文件格式（推荐 MP3）
- 在真机上测试（开发工具可能不支持某些音频格式）

#### 问题：广告功能报错
**解决方案：**
- 这是正常现象，开发环境下会显示模拟弹窗
- 真机测试需要配置正确的广告位ID
- 发布后需要等待广告审核

#### 问题：触摸事件无响应
**解决方案：**
- 检查 game.wxml 中的事件绑定
- 确认 game.js 中的事件处理函数存在
- 检查控制台是否有 JavaScript 错误

### 4. 性能测试

#### 内存使用
- 长时间游戏后检查内存是否正常
- 确认音频资源正确释放
- 检查定时器是否正确清理

#### 流畅度测试
- 蚊子移动是否流畅
- 长按动画是否卡顿
- 页面切换是否顺滑

### 5. 兼容性测试

#### 不同设备测试
- iPhone (iOS)
- Android 手机
- 不同屏幕尺寸

#### 微信版本测试
- 最新版微信
- 较旧版本微信（如果需要兼容）

### 6. 发布前检查

#### 必要配置
- [ ] 修改 project.config.json 中的 appid
- [ ] 配置正确的广告位ID
- [ ] 添加所有必需的资源文件
- [ ] 测试真机预览功能

#### 资源优化
- [ ] 压缩图片文件大小
- [ ] 优化音频文件大小
- [ ] 检查总包大小是否超过限制

#### 用户体验
- [ ] 游戏说明是否清晰
- [ ] 操作是否直观
- [ ] 错误提示是否友好

## 调试技巧

### 1. 控制台调试
打开微信开发者工具的控制台，查看：
- 错误信息
- 游戏状态日志
- 音频播放状态

### 2. 网络调试
如果涉及网络请求：
- 检查网络面板
- 确认请求是否成功
- 检查响应数据格式

### 3. 真机调试
- 使用真机调试功能
- 检查真机上的表现
- 测试音频和触摸功能

## 联系支持

如果遇到无法解决的问题：
1. 检查微信开发者文档
2. 查看小程序社区
3. 联系技术支持
