# 蚊子行为测试指南

## 🦟 新的蚊子移动逻辑

### 核心机制
1. **蚊子朝镜头移动**: 蚊子会慢慢变大（朝玩家移动）
2. **时间扣血**: 蚊子存活超过指定时间后扣血（第一关20秒，第二关15秒）
3. **声音控制**: 扣血时停止蚊子声音，3秒后重新生成蚊子并恢复声音
4. **即时反馈**: 击中蚊子时立即消失并停止声音

## 🎯 测试场景

### 场景1: 蚊子超时扣血
**测试步骤:**
1. 启动游戏，观察蚊子开始移动
2. 不进行任何击打操作
3. 等待20秒（第一关）或15秒（第二关）

**预期结果:**
- 蚊子正常移动和变大
- 当蚊子存活超过指定时间时：
  - 立即隐藏蚊子
  - 停止蚊子声音
  - 显示"蚊子逃跑了！-1血"提示
  - 血量减少1点
  - 设备震动
- 3秒后：
  - 重新生成新蚊子
  - 恢复蚊子声音播放

### 场景2: 击中蚊子
**测试步骤:**
1. 启动游戏
2. 在蚊子变大之前击中它

**预期结果:**
- 蚊子立即消失
- 停止蚊子声音
- 显示"击中!"效果
- 击杀数量+1
- 0.8秒后：
  - 重新生成新蚊子
  - 恢复蚊子声音播放

### 场景3: 连续扣血测试
**测试步骤:**
1. 让蚊子连续扣血3-4次
2. 观察血量变化和声音控制

**预期结果:**
- 每次扣血后都有3秒的静音期
- 血量正确递减
- 声音在新蚊子生成时正确恢复

### 场景4: 血量耗尽
**测试步骤:**
1. 让蚊子连续扣血直到血量为0

**预期结果:**
- 血量为0时游戏结束
- 显示"血量耗尽！"弹窗
- 停止所有声音和移动

## 🔊 声音测试重点

### 声音状态检查
- [ ] 游戏开始时蚊子声音正常播放
- [ ] 蚊子扣血时声音立即停止
- [ ] 3秒静音期内确实没有蚊子声音
- [ ] 新蚊子生成时声音正确恢复
- [ ] 击中蚊子时声音立即停止
- [ ] 击中后0.8秒新蚊子生成时声音恢复

### 音量测试
- [ ] 蚊子距离远时音量小
- [ ] 蚊子距离近时音量大
- [ ] 声音停止时确实静音
- [ ] 声音恢复时音量正确

## ⏱️ 时间测试

### 扣血间隔测试
- [ ] 扣血后到新蚊子生成确实是3秒
- [ ] 击中后到新蚊子生成确实是0.8秒
- [ ] 时间间隔稳定，不会因为其他操作影响

### 蚊子超时测试
- [ ] 第一关蚊子20秒后准确扣血
- [ ] 第二关蚊子15秒后准确扣血
- [ ] 时间计算准确，不会提前或延后扣血

## 🎮 游戏体验测试

### 难度平衡
- [ ] 第一关20秒时间足够击中蚊子
- [ ] 第二关15秒增加了时间压力
- [ ] 扣血频率合理，不会导致游戏无法进行
- [ ] 时间限制提供适当的挑战性

### 用户反馈
- [ ] 扣血提示清晰明显
- [ ] 震动反馈及时
- [ ] 血量显示准确
- [ ] 声音停止和恢复自然

## 🐛 常见问题排查

### 声音问题
**问题**: 声音没有停止或没有恢复
**检查**:
- 确认音频文件存在
- 检查 `startMosquitoSound()` 和 `stopMosquitoSound()` 调用
- 验证音频上下文是否正确创建

### 时间问题
**问题**: 延迟时间不准确
**检查**:
- 确认 `setTimeout` 时间设置正确
- 检查是否有其他定时器干扰
- 验证定时器是否被正确清理

### 扣血问题
**问题**: 扣血阈值不正确
**检查**:
- 确认 `mosquitoMaxScale` 设置为1.5
- 检查蚊子缩放计算逻辑
- 验证扣血条件判断

## 📊 性能测试

### 内存使用
- [ ] 长时间游戏后内存稳定
- [ ] 音频资源正确释放
- [ ] 定时器正确清理

### 流畅度
- [ ] 蚊子移动流畅
- [ ] 声音切换无卡顿
- [ ] UI更新及时

## 🔧 调试技巧

### 控制台日志
在浏览器控制台查看：
- 蚊子移动日志
- 声音播放/停止日志
- 扣血触发日志

### 真机测试
- 在真实设备上测试声音效果
- 验证震动反馈
- 检查性能表现

## ✅ 验收标准

所有以下条件都满足才算测试通过：
1. 蚊子变大到1.5倍时准确扣血
2. 扣血时蚊子立即消失且声音停止
3. 3秒后新蚊子生成且声音恢复
4. 击中蚊子时立即消失且声音停止
5. 0.8秒后新蚊子生成且声音恢复
6. 血量显示准确更新
7. 游戏结束逻辑正确
8. 声音控制完全正常
9. 时间间隔准确
10. 用户体验流畅自然
