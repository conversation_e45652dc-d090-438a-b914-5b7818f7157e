/* app.wxss */
/* 全局样式 */

page {
  background-color: #000000;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 通用按钮样式 */
.btn {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: #007aff;
  color: #ffffff;
}

.btn-primary:active {
  background-color: #0056cc;
  transform: scale(0.95);
}

.btn-secondary {
  background-color: #34c759;
  color: #ffffff;
}

.btn-secondary:active {
  background-color: #28a745;
  transform: scale(0.95);
}

.btn-warning {
  background-color: #ff9500;
  color: #ffffff;
}

.btn-warning:active {
  background-color: #e6850e;
  transform: scale(0.95);
}

.btn-danger {
  background-color: #ff3b30;
  color: #ffffff;
}

.btn-danger:active {
  background-color: #d70015;
  transform: scale(0.95);
}

.btn-disabled {
  background-color: #666666;
  color: #999999;
  pointer-events: none;
}

/* 文本样式 */
.text-center {
  text-align: center;
}

.text-large {
  font-size: 36rpx;
  font-weight: bold;
}

.text-medium {
  font-size: 28rpx;
}

.text-small {
  font-size: 24rpx;
}

/* 布局样式 */
.container {
  padding: 20rpx;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* 间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.mr-10 { margin-right: 10rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.pulse {
  animation: pulse 1s infinite;
}
