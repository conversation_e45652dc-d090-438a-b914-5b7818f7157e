<!-- pages/loading/loading.wxml -->
<view class="loading-container">
  
  <!-- 背景动画 -->
  <view class="background-animation">
    <view class="lightning-effect {{showLightning ? 'active' : ''}}"></view>
    <view class="danger-symbols">
      <text class="danger-symbol" style="left: 20%; top: 20%; animation-delay: 0s;">⚡</text>
      <text class="danger-symbol" style="left: 80%; top: 30%; animation-delay: 0.5s;">💀</text>
      <text class="danger-symbol" style="left: 60%; top: 60%; animation-delay: 1s;">⚠️</text>
      <text class="danger-symbol" style="left: 30%; top: 80%; animation-delay: 1.5s;">🔥</text>
      <text class="danger-symbol" style="left: 70%; top: 15%; animation-delay: 2s;">💥</text>
    </view>
  </view>

  <!-- 主要内容 -->
  <view class="content">
    
    <!-- 标题 -->
    <view class="title-section">
      <text class="main-title">难度飙升!</text>
      <text class="sub-title">准备进入第{{nextLevel}}关</text>
    </view>

    <!-- 警告信息 -->
    <view class="warning-section">
      <view class="warning-item">
        <text class="warning-icon">🌑</text>
        <text class="warning-text">屏幕亮度大幅降低</text>
      </view>
      <view class="warning-item">
        <text class="warning-icon">💨</text>
        <text class="warning-text">蚊子移动速度加快</text>
      </view>
      <view class="warning-item">
        <text class="warning-icon">🎯</text>
        <text class="warning-text">需要更精准的听声辨位</text>
      </view>
    </view>

    <!-- 进度条 -->
    <view class="progress-section">
      <text class="progress-text">加载中...</text>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{loadingProgress}}%;"></view>
      </view>
      <text class="progress-percent">{{loadingProgress}}%</text>
    </view>

    <!-- 提示信息 -->
    <view class="tips-section">
      <text class="tips-title">💡 游戏提示</text>
      <view class="tips-list">
        <text class="tip-item">• 仔细聆听蚊子的声音方向</text>
        <text class="tip-item">• 音量大小代表蚊子的远近</text>
        <text class="tip-item">• 长按时间决定击打距离</text>
        <text class="tip-item">• 善用广告功能提升能力</text>
      </view>
    </view>

  </view>

  <!-- 底部按钮 -->
  <view class="bottom-section">
    <button class="start-btn {{canStart ? 'active' : 'disabled'}}" 
            bindtap="startNextLevel" 
            disabled="{{!canStart}}">
      {{canStart ? '开始挑战' : '加载中...'}}
    </button>
  </view>

</view>
