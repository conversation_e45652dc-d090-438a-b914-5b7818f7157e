/* pages/loading/loading.wxss */

.loading-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #000000 50%, #2d1b1b 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40rpx 20rpx;
  box-sizing: border-box;
}

/* 背景动画 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.lightning-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.1s ease;
}

.lightning-effect.active {
  opacity: 1;
}

.danger-symbols {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.danger-symbol {
  position: absolute;
  font-size: 60rpx;
  opacity: 0.6;
  animation: dangerFloat 3s ease-in-out infinite;
}

/* 主要内容 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  position: relative;
}

/* 标题部分 */
.title-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.main-title {
  display: block;
  font-size: 72rpx;
  font-weight: bold;
  color: #ff4444;
  text-shadow: 0 0 20rpx rgba(255, 68, 68, 0.5);
  animation: titlePulse 2s ease-in-out infinite;
  margin-bottom: 20rpx;
}

.sub-title {
  display: block;
  font-size: 36rpx;
  color: #ffffff;
  opacity: 0.8;
}

/* 警告信息 */
.warning-section {
  width: 100%;
  max-width: 600rpx;
  margin-bottom: 60rpx;
}

.warning-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: rgba(255, 68, 68, 0.1);
  border-left: 6rpx solid #ff4444;
  border-radius: 8rpx;
}

.warning-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.warning-text {
  font-size: 28rpx;
  color: #ffffff;
  flex: 1;
}

/* 进度条 */
.progress-section {
  width: 100%;
  max-width: 500rpx;
  margin-bottom: 60rpx;
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 28rpx;
  color: #cccccc;
  margin-bottom: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444 0%, #ff8844 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
  animation: progressGlow 2s ease-in-out infinite;
}

.progress-percent {
  display: block;
  text-align: center;
  font-size: 24rpx;
  color: #999999;
}

/* 提示信息 */
.tips-section {
  width: 100%;
  max-width: 600rpx;
}

.tips-title {
  display: block;
  font-size: 32rpx;
  color: #ffaa00;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #cccccc;
  line-height: 1.5;
}

/* 底部按钮 */
.bottom-section {
  z-index: 10;
  position: relative;
}

.start-btn {
  width: 100%;
  padding: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 50rpx;
  border: none;
  transition: all 0.3s ease;
}

.start-btn.active {
  background: linear-gradient(135deg, #ff4444 0%, #ff8844 100%);
  color: #ffffff;
  box-shadow: 0 10rpx 30rpx rgba(255, 68, 68, 0.3);
}

.start-btn.active:active {
  transform: scale(0.95);
  box-shadow: 0 5rpx 15rpx rgba(255, 68, 68, 0.3);
}

.start-btn.disabled {
  background-color: #666666;
  color: #999999;
}

/* 动画 */
@keyframes titlePulse {
  0%, 100% {
    transform: scale(1);
    text-shadow: 0 0 20rpx rgba(255, 68, 68, 0.5);
  }
  50% {
    transform: scale(1.05);
    text-shadow: 0 0 30rpx rgba(255, 68, 68, 0.8);
  }
}

@keyframes dangerFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-20rpx) rotate(5deg);
  }
  50% {
    transform: translateY(-10rpx) rotate(-5deg);
  }
  75% {
    transform: translateY(-30rpx) rotate(3deg);
  }
}

@keyframes progressGlow {
  0%, 100% {
    box-shadow: 0 0 10rpx rgba(255, 68, 68, 0.3);
  }
  50% {
    box-shadow: 0 0 20rpx rgba(255, 68, 68, 0.6);
  }
}
