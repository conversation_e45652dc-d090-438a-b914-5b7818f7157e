// pages/loading/loading.js
const app = getApp();

Page({
  data: {
    nextLevel: 2,
    loadingProgress: 0,
    canStart: false,
    showLightning: false
  },

  onLoad() {
    console.log('加载页面启动');

    // 获取下一关卡信息
    const nextLevel = app.globalData.currentLevel;
    this.setData({
      nextLevel: nextLevel
    });

    // 开始加载动画
    this.startLoadingAnimation();
  },

  onShow() {
    console.log('加载页面显示');
  },

  onUnload() {
    console.log('加载页面卸载');
    this.clearAllTimers();
  },

  // 开始加载动画
  startLoadingAnimation() {
    // 闪电效果
    this.startLightningEffect();
    
    // 进度条动画
    this.startProgressAnimation();
    
    // 3秒后允许开始
    this.loadingTimer = setTimeout(() => {
      this.setData({
        canStart: true,
        loadingProgress: 100
      });
    }, 3000);
  },

  // 闪电效果
  startLightningEffect() {
    const showLightning = () => {
      this.setData({ showLightning: true });
      setTimeout(() => {
        this.setData({ showLightning: false });
      }, 100);
    };

    // 随机间隔显示闪电
    const scheduleNextLightning = () => {
      const delay = Math.random() * 2000 + 500; // 0.5-2.5秒随机间隔
      this.lightningTimer = setTimeout(() => {
        showLightning();
        scheduleNextLightning();
      }, delay);
    };

    scheduleNextLightning();
  },

  // 进度条动画
  startProgressAnimation() {
    let progress = 0;
    const duration = 3000; // 3秒
    const interval = 50; // 50ms更新一次
    const increment = (100 / duration) * interval;

    this.progressTimer = setInterval(() => {
      progress += increment;
      if (progress >= 100) {
        progress = 100;
        clearInterval(this.progressTimer);
      }
      
      this.setData({
        loadingProgress: Math.round(progress)
      });
    }, interval);
  },

  // 开始下一关
  startNextLevel() {
    if (!this.data.canStart) return;

    // 清理定时器
    this.clearAllTimers();

    // 播放按钮音效
    wx.vibrateShort();

    // 跳转回游戏页面
    wx.navigateBack({
      success: () => {
        // 返回成功后，游戏页面会自动重新初始化
        console.log('返回游戏页面');
      },
      fail: () => {
        // 如果返回失败，直接跳转到游戏页面
        wx.redirectTo({
          url: '/pages/game/game'
        });
      }
    });
  },

  // 清理所有定时器
  clearAllTimers() {
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
      this.loadingTimer = null;
    }
    
    if (this.lightningTimer) {
      clearTimeout(this.lightningTimer);
      this.lightningTimer = null;
    }
    
    if (this.progressTimer) {
      clearInterval(this.progressTimer);
      this.progressTimer = null;
    }
  }
});
