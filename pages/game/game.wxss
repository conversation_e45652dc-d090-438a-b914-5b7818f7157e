/* pages/game/game.wxss */

.game-container {
  width: 100vw;
  height: 100vh;
  background-color: #000000;
  position: relative;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

/* 背景图片 */
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  object-fit: cover;
}

/* 状态栏 */
.status-bar {
  position: fixed;
  top: 60rpx; /* 往下移动 */
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: rgba(0, 0, 0, 0.8); /* 降低透明度 */
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 200;
  padding-top: env(safe-area-inset-top);
  border-radius: 0 0 20rpx 20rpx; /* 添加圆角 */
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.status-label {
  font-size: 22rpx;
  color: #999999;
}

.status-value {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: bold;
}

.health-value {
  color: #00ff00;
}

.health-value.low-health {
  color: #ff4444;
  animation: healthWarning 1s ease-in-out infinite;
}

/* 血量条 */
.health-bar {
  position: fixed;
  top: 200rpx; /* 往下移动，给状态栏留出空间 */
  left: 20rpx;
  right: 20rpx;
  height: 60rpx;
  z-index: 200;
  background-color: rgba(0, 0, 0, 0.6); /* 添加背景 */
  border-radius: 15rpx; /* 添加圆角 */
  padding: 10rpx;
}

.health-background {
  width: 100%;
  height: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.health-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff4444 0%, #ffaa00 50%, #00ff00 100%);
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

.health-hearts {
  display: flex;
  justify-content: center;
  gap: 5rpx;
}

.heart {
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.heart.full {
  transform: scale(1.1);
}

.heart.empty {
  opacity: 0.5;
}

/* 游戏区域 */
.game-area {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 280rpx; /* 为新的HUD位置留出更多空间 */
  padding-bottom: 200rpx;
  z-index: 10; /* 在背景图片之上，但在HUD之下 */
}

/* 蚊子 */
.mosquito {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  transition: all 0.1s ease;
  z-index: 50; /* 在游戏区域之上 */
}

.mosquito.visible {
  opacity: 1;
}

.mosquito.hidden {
  opacity: 0;
}

.mosquito-image {
  width: 100%;
  height: 100%;
}

/* 长按指示器 */
.press-indicator {
  position: absolute;
  width: 0;
  height: 0;
  pointer-events: none;
  z-index: 100;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.press-indicator.active {
  opacity: 1;
}

.press-circle {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  border: 4rpx solid rgba(255, 255, 255, 0.6);
  transform-origin: center;
  left: -20rpx;
  top: -20rpx;
  transition: transform 0.1s ease;
}

.press-range {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 2rpx dashed rgba(255, 255, 255, 0.4);
  transform-origin: center;
  left: -50rpx;
  top: -50rpx;
  transition: transform 0.1s ease;
}

/* 距离指示器 */
.distance-indicator {
  position: absolute;
  top: -120rpx;
  left: -100rpx;
  width: 200rpx;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 10rpx;
  padding: 10rpx;
  text-align: center;
}

.distance-text {
  font-size: 24rpx;
  color: #ffffff;
  display: block;
  margin-bottom: 8rpx;
}

.distance-bar {
  width: 100%;
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  overflow: hidden;
}

.distance-fill {
  height: 100%;
  background: linear-gradient(90deg, #00ff00 0%, #ffaa00 50%, #ff4444 100%);
  border-radius: 4rpx;
  transition: width 0.1s ease;
}

/* 击打效果 */
.hit-effect {
  position: absolute;
  width: 0;
  height: 0;
  pointer-events: none;
  z-index: 150;
  opacity: 0;
}

.hit-effect.active {
  opacity: 1;
  animation: hitAnimation 0.5s ease-out forwards;
}

.hit-circle {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  left: -50rpx;
  top: -50rpx;
  animation: hitCircleExpand 0.5s ease-out forwards;
}

.hit-text {
  position: absolute;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.8);
  left: -50rpx;
  top: -80rpx;
  text-align: center;
  width: 100rpx;
  animation: hitTextFloat 0.5s ease-out forwards;
}

/* 广告区域 */
.ad-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.9);
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  z-index: 200;
}

.ad-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.ad-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  min-width: 120rpx;
  font-size: 24rpx;
}

.ad-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.ad-text {
  font-size: 20rpx;
}

/* 游戏结束弹窗 */
.game-over-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 300;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.game-over-modal.active {
  opacity: 1;
  pointer-events: all;
}

.modal-content {
  background-color: #1a1a1a;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
}

.modal-body {
  text-align: center;
  margin-bottom: 40rpx;
}

.modal-message {
  font-size: 32rpx;
  color: #cccccc;
  margin-bottom: 20rpx;
  display: block;
}

.game-stats {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.game-stats text {
  font-size: 28rpx;
  color: #999999;
}

.modal-footer {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.modal-footer .btn {
  width: 100%;
  padding: 24rpx;
  font-size: 32rpx;
}

/* 动画 */
@keyframes hitAnimation {
  0% {
    opacity: 1;
    transform: scale(0.5);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

@keyframes hitCircleExpand {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes hitTextFloat {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-60rpx);
    opacity: 0;
  }
}

@keyframes healthWarning {
  0%, 100% {
    transform: scale(1);
    text-shadow: 0 0 5rpx rgba(255, 68, 68, 0.5);
  }
  50% {
    transform: scale(1.1);
    text-shadow: 0 0 15rpx rgba(255, 68, 68, 1);
  }
}
