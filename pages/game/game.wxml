<!-- pages/game/game.wxml -->
<view class="game-container" style="opacity: {{screenBrightness}};">
  
  <!-- 游戏状态栏 -->
  <view class="status-bar">
    <view class="status-item">
      <text class="status-label">关卡:</text>
      <text class="status-value">{{currentLevel}}</text>
    </view>
    <view class="status-item">
      <text class="status-label">击杀:</text>
      <text class="status-value">{{killedCount}}/{{targetCount}}</text>
    </view>
    <view class="status-item">
      <text class="status-label">剩余:</text>
      <text class="status-value">{{remainingAttempts}}</text>
    </view>
  </view>

  <!-- 游戏区域 -->
  <view class="game-area" 
        bindtouchstart="onTouchStart"
        bindtouchmove="onTouchMove"
        bindtouchend="onTouchEnd">
    
    <!-- 蚊子 -->
    <view class="mosquito {{mosquito.visible ? 'visible' : 'hidden'}}"
          style="left: {{mosquito.x}}px; top: {{mosquito.y}}px; transform: scale({{mosquito.scale}});">
      <image src="/images/mosquito.png" class="mosquito-image"></image>
    </view>

    <!-- 长按指示器 -->
    <view class="press-indicator {{isPressing ? 'active' : ''}}"
          style="left: {{pressPosition.x}}px; top: {{pressPosition.y}}px;">
      <view class="press-circle" style="transform: scale({{pressScale}});"></view>
      <view class="press-range" style="transform: scale({{hitRangeScale}});"></view>
    </view>

    <!-- 击打效果 -->
    <view class="hit-effect {{showHitEffect ? 'active' : ''}}"
          style="left: {{hitPosition.x}}px; top: {{hitPosition.y}}px;">
      <view class="hit-circle"></view>
      <view class="hit-text">{{hitResult}}</view>
    </view>

  </view>

  <!-- 广告区域 -->
  <view class="ad-area">
    <view class="ad-buttons">
      <button class="ad-btn btn-secondary" bindtap="watchAdForHitRange">
        <text class="ad-icon">🎯</text>
        <text class="ad-text">扩大范围</text>
      </button>
      <button class="ad-btn btn-warning" bindtap="watchAdForBrightness">
        <text class="ad-icon">💡</text>
        <text class="ad-text">提高亮度</text>
      </button>
      <button class="ad-btn btn-primary" bindtap="watchAdForAttempts">
        <text class="ad-icon">🔄</text>
        <text class="ad-text">增加次数</text>
      </button>
    </view>
  </view>

  <!-- 游戏结束弹窗 -->
  <view class="game-over-modal {{showGameOver ? 'active' : ''}}" wx:if="{{showGameOver}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">{{gameOverTitle}}</text>
      </view>
      <view class="modal-body">
        <text class="modal-message">{{gameOverMessage}}</text>
        <view class="game-stats">
          <text>击杀: {{killedCount}}/{{targetCount}}</text>
          <text>命中率: {{hitRate}}%</text>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn btn-primary" bindtap="restartGame" wx:if="{{!showReviveOption}}">
          重新开始
        </button>
        <button class="btn btn-secondary" bindtap="nextLevel" wx:if="{{canNextLevel}}">
          下一关
        </button>
        <button class="btn btn-warning" bindtap="watchAdForRevive" wx:if="{{showReviveOption}}">
          看广告复活
        </button>
        <button class="btn btn-danger" bindtap="restartGame" wx:if="{{showReviveOption}}">
          重新开始
        </button>
      </view>
    </view>
  </view>

</view>

<!-- 音频组件 -->
<audio id="mosquitoSound" src="{{mosquitoAudioSrc}}" loop="{{true}}" hidden></audio>
<audio id="hitSound" src="/audio/hit.mp3" hidden></audio>
<audio id="missSound" src="/audio/miss.mp3" hidden></audio>
