// pages/game/game.js
const app = getApp();

Page({
  data: {
    // 游戏状态
    currentLevel: 1,
    killedCount: 0,
    targetCount: 10,
    remainingAttempts: 20,
    hitRate: 0,
    currentHealth: 10,
    maxHealth: 10,
    healthPercent: 100, // 血量百分比

    // 屏幕和显示
    screenBrightness: 0.6,
    screenWidth: 375,
    screenHeight: 667,

    // 蚊子相关
    mosquito: {
      x: 100,
      y: 100,
      z: 0.3, // 0-1, 0最远，1最近
      scale: 0.3,
      visible: true,
      speed: 1,
      zSpeed: 0.008, // 朝镜头移动速度
      direction: 1 // 1朝前，-1朝后
    },
    mosquitoAudioSrc: '/audio/mosquito.mp3',

    // 交互相关
    isPressing: false,
    pressPosition: { x: 0, y: 0 },
    pressStartTime: 0,
    pressScale: 1,
    hitRangeScale: 1,
    maxPressTime: 3000, // 最大长按时间(ms) - 增加到3秒
    pressDistance: 0, // 当前长按距离 0-1
    pressDistancePercent: 0, // 当前长按距离百分比

    // 击打相关
    showHitEffect: false,
    hitPosition: { x: 0, y: 0 },
    hitResult: '',
    hitRange: 50,

    // 游戏结束
    showGameOver: false,
    gameOverTitle: '',
    gameOverMessage: '',
    canNextLevel: false,
    showReviveOption: false
  },

  onLoad(options) {
    console.log('游戏页面加载');
    this.initGame();
  },

  onShow() {
    console.log('游戏页面显示');
    this.startGame();
  },

  onHide() {
    console.log('游戏页面隐藏');
    this.pauseGame();
  },

  onUnload() {
    console.log('游戏页面卸载');
    this.stopGame();
  },

  // 初始化游戏
  initGame() {
    // 获取屏幕尺寸
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      screenWidth: systemInfo.windowWidth,
      screenHeight: systemInfo.windowHeight
    });

    // 获取当前关卡配置
    const levelConfig = app.getCurrentLevelConfig();
    this.setData({
      currentLevel: app.globalData.currentLevel,
      targetCount: levelConfig.mosquitoCount,
      remainingAttempts: levelConfig.maxAttempts,
      screenBrightness: levelConfig.brightness,
      hitRange: levelConfig.hitRange,
      maxHealth: levelConfig.maxHealth,
      currentHealth: levelConfig.maxHealth,
      healthPercent: 100,
      'mosquito.speed': levelConfig.mosquitoSpeed,
      'mosquito.zSpeed': levelConfig.mosquitoZSpeed
    });

    // 初始化音频
    this.initAudio();

    // 重置游戏数据
    this.resetGameData();
  },

  // 初始化音频
  initAudio() {
    // 蚊子声音
    this.mosquitoAudio = wx.createInnerAudioContext();
    this.mosquitoAudio.src = this.data.mosquitoAudioSrc;
    this.mosquitoAudio.loop = true;
    this.mosquitoAudio.volume = 0.5;

    // 击打声音
    this.hitAudio = wx.createInnerAudioContext();
    this.hitAudio.src = '/audio/hit.mp3';
    
    // 未击中声音
    this.missAudio = wx.createInnerAudioContext();
    this.missAudio.src = '/audio/miss.mp3';
  },

  // 重置游戏数据
  resetGameData() {
    const levelConfig = app.getCurrentLevelConfig();
    this.setData({
      killedCount: 0,
      hitRate: 0,
      currentHealth: levelConfig.maxHealth,
      healthPercent: 100,
      showGameOver: false,
      canNextLevel: false,
      showReviveOption: false
    });
  },

  // 开始游戏
  startGame() {
    console.log('开始游戏');
    this.spawnMosquito();
    this.startMosquitoMovement();
    this.startMosquitoSound();
  },

  // 暂停游戏
  pauseGame() {
    console.log('暂停游戏');
    this.stopMosquitoMovement();
    this.stopMosquitoSound();
  },

  // 停止游戏
  stopGame() {
    console.log('停止游戏');
    this.stopMosquitoMovement();
    this.stopMosquitoSound();
    
    // 清理音频资源
    if (this.mosquitoAudio) {
      this.mosquitoAudio.destroy();
    }
    if (this.hitAudio) {
      this.hitAudio.destroy();
    }
    if (this.missAudio) {
      this.missAudio.destroy();
    }
  },

  // 生成蚊子
  spawnMosquito() {
    const x = Math.random() * (this.data.screenWidth - 60);
    const y = Math.random() * (this.data.screenHeight - 200) + 100;
    const z = 0.2 + Math.random() * 0.1; // 从较远处开始 0.2-0.3

    this.setData({
      'mosquito.x': x,
      'mosquito.y': y,
      'mosquito.z': z,
      'mosquito.scale': z,
      'mosquito.visible': true,
      'mosquito.direction': 1 // 重置为朝前移动
    });
  },

  // 开始蚊子移动
  startMosquitoMovement() {
    this.mosquitoMoveInterval = setInterval(() => {
      this.moveMosquito();
    }, 100);
  },

  // 停止蚊子移动
  stopMosquitoMovement() {
    if (this.mosquitoMoveInterval) {
      clearInterval(this.mosquitoMoveInterval);
      this.mosquitoMoveInterval = null;
    }
  },

  // 移动蚊子
  moveMosquito() {
    const mosquito = this.data.mosquito;
    const speed = mosquito.speed;
    const zSpeed = mosquito.zSpeed;
    const levelConfig = app.getCurrentLevelConfig();

    // 水平移动
    let newX = mosquito.x + (Math.random() - 0.5) * speed * 10;
    newX = Math.max(0, Math.min(newX, this.data.screenWidth - 60));

    // Z轴移动（朝镜头移动）
    let newZ = mosquito.z + mosquito.direction * zSpeed;

    // 检查是否到达边界，需要反向
    if (newZ >= 1.0) {
      newZ = 1.0;
      this.setData({
        'mosquito.direction': -1 // 开始朝后移动
      });
    } else if (newZ <= 0.2) {
      newZ = 0.2;
      this.setData({
        'mosquito.direction': 1 // 开始朝前移动
      });
    }

    // 检查是否超过扣血阈值
    if (newZ >= levelConfig.mosquitoMaxScale && mosquito.direction === 1) {
      this.handleMosquitoReachPlayer();
      return; // 不继续移动，等待重新生成
    }

    this.setData({
      'mosquito.x': newX,
      'mosquito.z': newZ,
      'mosquito.scale': newZ
    });

    // 更新音频音量
    this.updateMosquitoSoundVolume();
  },

  // 开始蚊子声音
  startMosquitoSound() {
    if (this.mosquitoAudio) {
      this.mosquitoAudio.play();
      this.updateMosquitoSoundVolume();
    }
  },

  // 停止蚊子声音
  stopMosquitoSound() {
    if (this.mosquitoAudio) {
      this.mosquitoAudio.pause();
    }
  },

  // 更新蚊子声音音量
  updateMosquitoSoundVolume() {
    if (this.mosquitoAudio) {
      // 根据Z轴距离调整音量，越近声音越大
      const volume = this.data.mosquito.z * 0.8;
      this.mosquitoAudio.volume = volume;
    }
  },

  // 处理蚊子到达玩家（扣血）
  handleMosquitoReachPlayer() {
    const newHealth = Math.max(0, this.data.currentHealth - 1);
    const healthPercent = Math.round((newHealth / this.data.maxHealth) * 100);

    this.setData({
      currentHealth: newHealth,
      healthPercent: healthPercent
    });

    // 显示扣血效果
    wx.showToast({
      title: '被蚊子咬了！-1血',
      icon: 'none',
      duration: 1500
    });

    // 震动反馈
    wx.vibrateShort();

    // 检查是否死亡
    if (newHealth <= 0) {
      this.handlePlayerDeath();
      return;
    }

    // 重新生成蚊子
    setTimeout(() => {
      this.spawnMosquito();
    }, 1000);
  },

  // 处理玩家死亡
  handlePlayerDeath() {
    this.stopGame();

    this.setData({
      showGameOver: true,
      gameOverTitle: '血量耗尽！',
      gameOverMessage: '被蚊子咬死了，要重新开始吗？',
      canNextLevel: false,
      showReviveOption: true
    });
  },

  // 触摸开始
  onTouchStart(e) {
    const touch = e.touches[0];
    this.setData({
      isPressing: true,
      pressPosition: {
        x: touch.clientX,
        y: touch.clientY
      },
      pressStartTime: Date.now(),
      pressScale: 1
    });

    // 开始长按动画
    this.startPressAnimation();
  },

  // 触摸移动
  onTouchMove(e) {
    if (!this.data.isPressing) return;

    const touch = e.touches[0];
    this.setData({
      pressPosition: {
        x: touch.clientX,
        y: touch.clientY
      }
    });
  },

  // 触摸结束
  onTouchEnd(e) {
    if (!this.data.isPressing) return;

    const touch = e.changedTouches[0];
    const hitDistance = this.data.pressDistance; // 使用当前的距离值

    this.setData({
      isPressing: false,
      pressScale: 1,
      pressDistance: 0,
      pressDistancePercent: 0
    });

    // 停止长按动画
    this.stopPressAnimation();

    // 执行击打
    this.performHit({
      x: touch.clientX,
      y: touch.clientY,
      distance: hitDistance
    });
  },

  // 开始长按动画
  startPressAnimation() {
    this.pressAnimationInterval = setInterval(() => {
      const pressTime = Date.now() - this.data.pressStartTime;
      const progress = (pressTime % this.data.maxPressTime) / this.data.maxPressTime;

      // 创建来回循环的距离值 (0 -> 1 -> 0 -> 1...)
      let distance;
      if (progress <= 0.5) {
        // 前半段：从近到远 (0 -> 1)
        distance = progress * 2;
      } else {
        // 后半段：从远到近 (1 -> 0)
        distance = 2 - (progress * 2);
      }

      const scale = 1 + distance * 2;

      this.setData({
        pressScale: scale,
        hitRangeScale: 1 + distance * 0.5,
        pressDistance: distance,
        pressDistancePercent: Math.round(distance * 100)
      });
    }, 50);
  },

  // 停止长按动画
  stopPressAnimation() {
    if (this.pressAnimationInterval) {
      clearInterval(this.pressAnimationInterval);
      this.pressAnimationInterval = null;
    }
  },

  // 执行击打
  performHit(hitInfo) {
    const mosquito = this.data.mosquito;

    // 计算2D距离（屏幕位置）
    const distance2D = Math.sqrt(
      Math.pow(hitInfo.x - mosquito.x - 30, 2) +
      Math.pow(hitInfo.y - mosquito.y - 30, 2)
    );

    // 计算Z轴距离差（深度匹配）
    const zDistance = Math.abs(hitInfo.distance - mosquito.z);

    // 击中判定：2D距离在范围内 且 Z轴距离匹配
    const isInRange = distance2D <= this.data.hitRange;
    const isZMatched = zDistance <= 0.2; // Z轴容错范围
    const isHit = isInRange && isZMatched;

    // 减少剩余次数
    const newAttempts = this.data.remainingAttempts - 1;
    this.setData({
      remainingAttempts: newAttempts
    });

    if (isHit) {
      this.handleHitSuccess(hitInfo);
    } else {
      this.handleHitMiss(hitInfo, { isInRange, isZMatched, zDistance });
    }

    // 播放击打音效
    this.playHitSound();

    // 检查游戏状态
    this.checkGameStatus();
  },

  // 处理击中成功
  handleHitSuccess(hitInfo) {
    const newKilledCount = this.data.killedCount + 1;
    this.setData({
      killedCount: newKilledCount,
      showHitEffect: true,
      hitPosition: { x: hitInfo.x, y: hitInfo.y },
      hitResult: '击中!'
    });

    // 隐藏击打效果
    setTimeout(() => {
      this.setData({ showHitEffect: false });
    }, 500);

    // 生成新蚊子
    setTimeout(() => {
      this.spawnMosquito();
    }, 1000);

    // 更新命中率
    this.updateHitRate();
  },

  // 处理击打失败
  handleHitMiss(hitInfo, debugInfo) {
    let missReason = '未击中';

    if (debugInfo) {
      if (!debugInfo.isInRange) {
        missReason = '位置偏了';
      } else if (!debugInfo.isZMatched) {
        missReason = debugInfo.zDistance > 0.2 ? '距离不对' : '深度不匹配';
      }
    }

    this.setData({
      showHitEffect: true,
      hitPosition: { x: hitInfo.x, y: hitInfo.y },
      hitResult: missReason
    });

    // 隐藏击打效果
    setTimeout(() => {
      this.setData({ showHitEffect: false });
    }, 500);

    // 更新命中率
    this.updateHitRate();
  },

  // 更新命中率
  updateHitRate() {
    const totalAttempts = app.getCurrentLevelConfig().maxAttempts - this.data.remainingAttempts;
    const hitRate = totalAttempts > 0 ? Math.round((this.data.killedCount / totalAttempts) * 100) : 0;
    this.setData({ hitRate });
  },

  // 播放击打音效
  playHitSound() {
    if (this.hitAudio) {
      this.hitAudio.play();
    }
  },

  // 检查游戏状态
  checkGameStatus() {
    const { killedCount, targetCount, remainingAttempts, currentHealth } = this.data;

    // 检查是否死亡
    if (currentHealth <= 0) {
      this.handlePlayerDeath();
      return;
    }

    // 检查是否完成关卡
    if (killedCount >= targetCount) {
      this.handleLevelComplete();
      return;
    }

    // 检查是否用完次数
    if (remainingAttempts <= 0) {
      this.handleGameOver();
      return;
    }
  },

  // 处理关卡完成
  handleLevelComplete() {
    this.stopGame();

    const isLastLevel = this.data.currentLevel >= app.globalData.totalLevels;

    this.setData({
      showGameOver: true,
      gameOverTitle: isLastLevel ? '游戏通关!' : '关卡完成!',
      gameOverMessage: isLastLevel ? '恭喜你完成了所有关卡!' : '准备进入下一关卡',
      canNextLevel: !isLastLevel,
      showReviveOption: false
    });
  },

  // 处理游戏结束
  handleGameOver() {
    this.stopGame();

    this.setData({
      showGameOver: true,
      gameOverTitle: '游戏结束',
      gameOverMessage: '次数用完了，要重新开始吗？',
      canNextLevel: false,
      showReviveOption: true
    });
  },

  // 重新开始游戏
  restartGame() {
    app.resetGameData();
    this.initGame();
    this.startGame();
  },

  // 下一关
  nextLevel() {
    app.setCurrentLevel(this.data.currentLevel + 1);

    // 跳转到加载页面
    wx.navigateTo({
      url: '/pages/loading/loading'
    });
  },

  // 看广告增加击打范围
  watchAdForHitRange() {
    this.showRewardedVideoAd(() => {
      const newHitRange = this.data.hitRange + app.globalData.adConfig.hitRangeBonus;
      this.setData({ hitRange: newHitRange });

      wx.showToast({
        title: '击打范围增加!',
        icon: 'success'
      });
    });
  },

  // 看广告提高屏幕亮度
  watchAdForBrightness() {
    this.showRewardedVideoAd(() => {
      const newBrightness = Math.min(1, this.data.screenBrightness + app.globalData.adConfig.brightnessBonus);
      this.setData({ screenBrightness: newBrightness });

      wx.showToast({
        title: '屏幕亮度提高!',
        icon: 'success'
      });
    });
  },

  // 看广告增加击打次数
  watchAdForAttempts() {
    this.showRewardedVideoAd(() => {
      const newAttempts = this.data.remainingAttempts + app.globalData.adConfig.attemptsBonus;
      this.setData({ remainingAttempts: newAttempts });

      wx.showToast({
        title: '击打次数增加!',
        icon: 'success'
      });
    });
  },

  // 看广告复活
  watchAdForRevive() {
    this.showRewardedVideoAd(() => {
      const newAttempts = app.globalData.adConfig.reviveAttempts;
      this.setData({
        remainingAttempts: newAttempts,
        showGameOver: false
      });

      wx.showToast({
        title: '复活成功!',
        icon: 'success'
      });

      // 重新开始游戏
      this.startGame();
    });
  },

  // 显示激励视频广告
  showRewardedVideoAd(onSuccess) {
    // 检查是否支持激励视频广告
    if (typeof wx.createRewardedVideoAd === 'function') {
      try {
        // 创建激励视频广告实例
        const rewardedVideoAd = wx.createRewardedVideoAd({
          adUnitId: 'adunit-xxxxxxxxxxxxxxxx' // 请替换为你的广告位ID
        });

        // 广告加载成功
        rewardedVideoAd.onLoad(() => {
          console.log('激励视频广告加载成功');
        });

        // 广告加载失败
        rewardedVideoAd.onError((err) => {
          console.error('激励视频广告错误:', err);
          this.handleAdError(onSuccess);
        });

        // 广告关闭
        rewardedVideoAd.onClose((res) => {
          if (res && res.isEnded) {
            // 用户看完广告，给予奖励
            console.log('用户看完广告');
            onSuccess();
          } else {
            // 用户中途关闭广告
            console.log('用户中途关闭广告');
            wx.showToast({
              title: '请看完广告获得奖励',
              icon: 'none',
              duration: 2000
            });
          }
        });

        // 显示广告
        rewardedVideoAd.show().catch((err) => {
          console.error('广告显示失败:', err);
          // 尝试重新加载并显示
          rewardedVideoAd.load().then(() => {
            return rewardedVideoAd.show();
          }).catch((loadErr) => {
            console.error('广告重新加载失败:', loadErr);
            this.handleAdError(onSuccess);
          });
        });

      } catch (error) {
        console.error('创建广告实例失败:', error);
        this.handleAdError(onSuccess);
      }
    } else {
      // 不支持激励视频广告（开发环境或旧版本）
      console.log('当前环境不支持激励视频广告');
      this.handleAdError(onSuccess);
    }
  },

  // 处理广告错误
  handleAdError(onSuccess) {
    wx.showModal({
      title: '广告暂不可用',
      content: '广告功能暂时不可用，是否直接获得奖励？\n(开发环境下会直接给予奖励)',
      confirmText: '确定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          console.log('用户选择直接获得奖励');
          onSuccess();
        } else {
          console.log('用户取消获得奖励');
        }
      }
    });
  }
});
