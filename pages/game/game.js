// pages/game/game.js
const app = getApp();

Page({
  data: {
    // 游戏状态
    currentLevel: 1,
    killedCount: 0,
    targetCount: 10,
    remainingAttempts: 20,
    hitRate: 0,
    
    // 屏幕和显示
    screenBrightness: 0.3,
    screenWidth: 375,
    screenHeight: 667,
    
    // 蚊子相关
    mosquito: {
      x: 100,
      y: 100,
      z: 0.5, // 0-1, 0最远，1最近
      scale: 0.5,
      visible: true,
      speed: 1
    },
    mosquitoAudioSrc: '/audio/mosquito.mp3',
    
    // 交互相关
    isPressing: false,
    pressPosition: { x: 0, y: 0 },
    pressStartTime: 0,
    pressScale: 1,
    hitRangeScale: 1,
    maxPressTime: 2000, // 最大长按时间(ms)
    
    // 击打相关
    showHitEffect: false,
    hitPosition: { x: 0, y: 0 },
    hitResult: '',
    hitRange: 50,
    
    // 游戏结束
    showGameOver: false,
    gameOverTitle: '',
    gameOverMessage: '',
    canNextLevel: false,
    showReviveOption: false
  },

  onLoad(options) {
    console.log('游戏页面加载');
    this.initGame();
  },

  onShow() {
    console.log('游戏页面显示');
    this.startGame();
  },

  onHide() {
    console.log('游戏页面隐藏');
    this.pauseGame();
  },

  onUnload() {
    console.log('游戏页面卸载');
    this.stopGame();
  },

  // 初始化游戏
  initGame() {
    // 获取屏幕尺寸
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      screenWidth: systemInfo.windowWidth,
      screenHeight: systemInfo.windowHeight
    });

    // 获取当前关卡配置
    const levelConfig = app.getCurrentLevelConfig();
    this.setData({
      currentLevel: app.globalData.currentLevel,
      targetCount: levelConfig.mosquitoCount,
      remainingAttempts: levelConfig.maxAttempts,
      screenBrightness: levelConfig.brightness,
      hitRange: levelConfig.hitRange,
      'mosquito.speed': levelConfig.mosquitoSpeed
    });

    // 初始化音频
    this.initAudio();
    
    // 重置游戏数据
    this.resetGameData();
  },

  // 初始化音频
  initAudio() {
    // 蚊子声音
    this.mosquitoAudio = wx.createInnerAudioContext();
    this.mosquitoAudio.src = this.data.mosquitoAudioSrc;
    this.mosquitoAudio.loop = true;
    this.mosquitoAudio.volume = 0.5;

    // 击打声音
    this.hitAudio = wx.createInnerAudioContext();
    this.hitAudio.src = '/audio/hit.mp3';
    
    // 未击中声音
    this.missAudio = wx.createInnerAudioContext();
    this.missAudio.src = '/audio/miss.mp3';
  },

  // 重置游戏数据
  resetGameData() {
    this.setData({
      killedCount: 0,
      hitRate: 0,
      showGameOver: false,
      canNextLevel: false,
      showReviveOption: false
    });
  },

  // 开始游戏
  startGame() {
    console.log('开始游戏');
    this.spawnMosquito();
    this.startMosquitoMovement();
    this.startMosquitoSound();
  },

  // 暂停游戏
  pauseGame() {
    console.log('暂停游戏');
    this.stopMosquitoMovement();
    this.stopMosquitoSound();
  },

  // 停止游戏
  stopGame() {
    console.log('停止游戏');
    this.stopMosquitoMovement();
    this.stopMosquitoSound();
    
    // 清理音频资源
    if (this.mosquitoAudio) {
      this.mosquitoAudio.destroy();
    }
    if (this.hitAudio) {
      this.hitAudio.destroy();
    }
    if (this.missAudio) {
      this.missAudio.destroy();
    }
  },

  // 生成蚊子
  spawnMosquito() {
    const x = Math.random() * (this.data.screenWidth - 60);
    const y = Math.random() * (this.data.screenHeight - 200) + 100;
    const z = Math.random() * 0.8 + 0.2; // 0.2-1.0
    
    this.setData({
      'mosquito.x': x,
      'mosquito.y': y,
      'mosquito.z': z,
      'mosquito.scale': z,
      'mosquito.visible': true
    });
  },

  // 开始蚊子移动
  startMosquitoMovement() {
    this.mosquitoMoveInterval = setInterval(() => {
      this.moveMosquito();
    }, 100);
  },

  // 停止蚊子移动
  stopMosquitoMovement() {
    if (this.mosquitoMoveInterval) {
      clearInterval(this.mosquitoMoveInterval);
      this.mosquitoMoveInterval = null;
    }
  },

  // 移动蚊子
  moveMosquito() {
    const mosquito = this.data.mosquito;
    const speed = mosquito.speed;

    // 水平移动
    let newX = mosquito.x + (Math.random() - 0.5) * speed * 10;
    newX = Math.max(0, Math.min(newX, this.data.screenWidth - 60));

    // Z轴移动（远近）
    let newZ = mosquito.z + (Math.random() - 0.5) * 0.02;
    newZ = Math.max(0.2, Math.min(newZ, 1.0));

    this.setData({
      'mosquito.x': newX,
      'mosquito.z': newZ,
      'mosquito.scale': newZ
    });

    // 更新音频音量
    this.updateMosquitoSoundVolume();
  },

  // 开始蚊子声音
  startMosquitoSound() {
    if (this.mosquitoAudio) {
      this.mosquitoAudio.play();
      this.updateMosquitoSoundVolume();
    }
  },

  // 停止蚊子声音
  stopMosquitoSound() {
    if (this.mosquitoAudio) {
      this.mosquitoAudio.pause();
    }
  },

  // 更新蚊子声音音量
  updateMosquitoSoundVolume() {
    if (this.mosquitoAudio) {
      // 根据Z轴距离调整音量，越近声音越大
      const volume = this.data.mosquito.z * 0.8;
      this.mosquitoAudio.volume = volume;
    }
  },

  // 触摸开始
  onTouchStart(e) {
    const touch = e.touches[0];
    this.setData({
      isPressing: true,
      pressPosition: {
        x: touch.clientX,
        y: touch.clientY
      },
      pressStartTime: Date.now(),
      pressScale: 1
    });

    // 开始长按动画
    this.startPressAnimation();
  },

  // 触摸移动
  onTouchMove(e) {
    if (!this.data.isPressing) return;

    const touch = e.touches[0];
    this.setData({
      pressPosition: {
        x: touch.clientX,
        y: touch.clientY
      }
    });
  },

  // 触摸结束
  onTouchEnd(e) {
    if (!this.data.isPressing) return;

    const touch = e.changedTouches[0];
    const pressTime = Date.now() - this.data.pressStartTime;
    const hitDistance = Math.min(pressTime / this.data.maxPressTime, 1);

    this.setData({
      isPressing: false,
      pressScale: 1
    });

    // 停止长按动画
    this.stopPressAnimation();

    // 执行击打
    this.performHit({
      x: touch.clientX,
      y: touch.clientY,
      distance: hitDistance
    });
  },

  // 开始长按动画
  startPressAnimation() {
    this.pressAnimationInterval = setInterval(() => {
      const pressTime = Date.now() - this.data.pressStartTime;
      const progress = Math.min(pressTime / this.data.maxPressTime, 1);
      const scale = 1 + progress * 2;

      this.setData({
        pressScale: scale,
        hitRangeScale: 1 + progress * 0.5
      });
    }, 50);
  },

  // 停止长按动画
  stopPressAnimation() {
    if (this.pressAnimationInterval) {
      clearInterval(this.pressAnimationInterval);
      this.pressAnimationInterval = null;
    }
  },

  // 执行击打
  performHit(hitInfo) {
    const mosquito = this.data.mosquito;
    const distance = Math.sqrt(
      Math.pow(hitInfo.x - mosquito.x - 30, 2) +
      Math.pow(hitInfo.y - mosquito.y - 30, 2)
    );

    // 计算有效击打范围（考虑Z轴距离）
    const effectiveRange = this.data.hitRange * hitInfo.distance * mosquito.z;
    const isHit = distance <= effectiveRange;

    // 减少剩余次数
    const newAttempts = this.data.remainingAttempts - 1;
    this.setData({
      remainingAttempts: newAttempts
    });

    if (isHit) {
      this.handleHitSuccess(hitInfo);
    } else {
      this.handleHitMiss(hitInfo);
    }

    // 播放击打音效
    this.playHitSound();

    // 检查游戏状态
    this.checkGameStatus();
  },

  // 处理击中成功
  handleHitSuccess(hitInfo) {
    const newKilledCount = this.data.killedCount + 1;
    this.setData({
      killedCount: newKilledCount,
      showHitEffect: true,
      hitPosition: { x: hitInfo.x, y: hitInfo.y },
      hitResult: '击中!'
    });

    // 隐藏击打效果
    setTimeout(() => {
      this.setData({ showHitEffect: false });
    }, 500);

    // 生成新蚊子
    setTimeout(() => {
      this.spawnMosquito();
    }, 1000);

    // 更新命中率
    this.updateHitRate();
  },

  // 处理击打失败
  handleHitMiss(hitInfo) {
    this.setData({
      showHitEffect: true,
      hitPosition: { x: hitInfo.x, y: hitInfo.y },
      hitResult: '未击中'
    });

    // 隐藏击打效果
    setTimeout(() => {
      this.setData({ showHitEffect: false });
    }, 500);

    // 更新命中率
    this.updateHitRate();
  },

  // 更新命中率
  updateHitRate() {
    const totalAttempts = app.getCurrentLevelConfig().maxAttempts - this.data.remainingAttempts;
    const hitRate = totalAttempts > 0 ? Math.round((this.data.killedCount / totalAttempts) * 100) : 0;
    this.setData({ hitRate });
  },

  // 播放击打音效
  playHitSound() {
    if (this.hitAudio) {
      this.hitAudio.play();
    }
  },

  // 检查游戏状态
  checkGameStatus() {
    const { killedCount, targetCount, remainingAttempts } = this.data;

    // 检查是否完成关卡
    if (killedCount >= targetCount) {
      this.handleLevelComplete();
      return;
    }

    // 检查是否用完次数
    if (remainingAttempts <= 0) {
      this.handleGameOver();
      return;
    }
  },

  // 处理关卡完成
  handleLevelComplete() {
    this.stopGame();

    const isLastLevel = this.data.currentLevel >= app.globalData.totalLevels;

    this.setData({
      showGameOver: true,
      gameOverTitle: isLastLevel ? '游戏通关!' : '关卡完成!',
      gameOverMessage: isLastLevel ? '恭喜你完成了所有关卡!' : '准备进入下一关卡',
      canNextLevel: !isLastLevel,
      showReviveOption: false
    });
  },

  // 处理游戏结束
  handleGameOver() {
    this.stopGame();

    this.setData({
      showGameOver: true,
      gameOverTitle: '游戏结束',
      gameOverMessage: '次数用完了，要重新开始吗？',
      canNextLevel: false,
      showReviveOption: true
    });
  },

  // 重新开始游戏
  restartGame() {
    app.resetGameData();
    this.initGame();
    this.startGame();
  },

  // 下一关
  nextLevel() {
    app.setCurrentLevel(this.data.currentLevel + 1);

    // 跳转到加载页面
    wx.navigateTo({
      url: '/pages/loading/loading'
    });
  },

  // 看广告增加击打范围
  watchAdForHitRange() {
    this.showRewardedVideoAd(() => {
      const newHitRange = this.data.hitRange + app.globalData.adConfig.hitRangeBonus;
      this.setData({ hitRange: newHitRange });

      wx.showToast({
        title: '击打范围增加!',
        icon: 'success'
      });
    });
  },

  // 看广告提高屏幕亮度
  watchAdForBrightness() {
    this.showRewardedVideoAd(() => {
      const newBrightness = Math.min(1, this.data.screenBrightness + app.globalData.adConfig.brightnessBonus);
      this.setData({ screenBrightness: newBrightness });

      wx.showToast({
        title: '屏幕亮度提高!',
        icon: 'success'
      });
    });
  },

  // 看广告增加击打次数
  watchAdForAttempts() {
    this.showRewardedVideoAd(() => {
      const newAttempts = this.data.remainingAttempts + app.globalData.adConfig.attemptsBonus;
      this.setData({ remainingAttempts: newAttempts });

      wx.showToast({
        title: '击打次数增加!',
        icon: 'success'
      });
    });
  },

  // 看广告复活
  watchAdForRevive() {
    this.showRewardedVideoAd(() => {
      const newAttempts = app.globalData.adConfig.reviveAttempts;
      this.setData({
        remainingAttempts: newAttempts,
        showGameOver: false
      });

      wx.showToast({
        title: '复活成功!',
        icon: 'success'
      });

      // 重新开始游戏
      this.startGame();
    });
  },

  // 显示激励视频广告
  showRewardedVideoAd(onSuccess) {
    // 创建激励视频广告实例
    if (wx.createRewardedVideoAd) {
      const rewardedVideoAd = wx.createRewardedVideoAd({
        adUnitId: 'your-ad-unit-id' // 替换为你的广告位ID
      });

      rewardedVideoAd.onLoad(() => {
        console.log('激励视频广告加载成功');
      });

      rewardedVideoAd.onError((err) => {
        console.error('激励视频广告加载失败', err);
        wx.showToast({
          title: '广告加载失败',
          icon: 'none'
        });
      });

      rewardedVideoAd.onClose((res) => {
        if (res && res.isEnded) {
          // 用户看完广告
          onSuccess();
        } else {
          // 用户中途关闭广告
          wx.showToast({
            title: '请看完广告获得奖励',
            icon: 'none'
          });
        }
      });

      // 显示广告
      rewardedVideoAd.show().catch(() => {
        // 广告显示失败，可能是广告还没加载好
        rewardedVideoAd.load().then(() => rewardedVideoAd.show());
      });
    } else {
      // 不支持激励视频广告，直接给奖励（开发环境）
      wx.showModal({
        title: '提示',
        content: '当前环境不支持广告，直接给予奖励',
        success: (res) => {
          if (res.confirm) {
            onSuccess();
          }
        }
      });
    }
  }
});
