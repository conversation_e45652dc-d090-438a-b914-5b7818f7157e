// utils/game.js
// 游戏工具函数

// 计算两点之间的距离
function calculateDistance(point1, point2) {
  return Math.sqrt(
    Math.pow(point2.x - point1.x, 2) + 
    Math.pow(point2.y - point1.y, 2)
  );
}

// 计算3D距离
function calculate3DDistance(point1, point2) {
  return Math.sqrt(
    Math.pow(point2.x - point1.x, 2) + 
    Math.pow(point2.y - point1.y, 2) + 
    Math.pow(point2.z - point1.z, 2)
  );
}

// 生成随机位置
function generateRandomPosition(bounds) {
  return {
    x: Math.random() * (bounds.maxX - bounds.minX) + bounds.minX,
    y: Math.random() * (bounds.maxY - bounds.minY) + bounds.minY,
    z: Math.random() * (bounds.maxZ - bounds.minZ) + bounds.minZ
  };
}

// 限制值在指定范围内
function clamp(value, min, max) {
  return Math.max(min, Math.min(max, value));
}

// 线性插值
function lerp(start, end, factor) {
  return start + (end - start) * factor;
}

// 缓动函数
const easing = {
  // 线性
  linear: (t) => t,
  
  // 二次方缓入
  easeInQuad: (t) => t * t,
  
  // 二次方缓出
  easeOutQuad: (t) => t * (2 - t),
  
  // 二次方缓入缓出
  easeInOutQuad: (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  
  // 弹性缓出
  easeOutElastic: (t) => {
    const c4 = (2 * Math.PI) / 3;
    return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
  }
};

// 角度转弧度
function degToRad(degrees) {
  return degrees * (Math.PI / 180);
}

// 弧度转角度
function radToDeg(radians) {
  return radians * (180 / Math.PI);
}

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流函数
function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 格式化时间
function formatTime(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// 格式化分数
function formatScore(score) {
  return score.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 计算命中率
function calculateHitRate(hits, total) {
  if (total === 0) return 0;
  return Math.round((hits / total) * 100);
}

// 游戏难度配置
const DIFFICULTY_CONFIGS = {
  easy: {
    mosquitoSpeed: 1,
    brightness: 0.5,
    hitRange: 60,
    maxAttempts: 30
  },
  normal: {
    mosquitoSpeed: 2,
    brightness: 0.3,
    hitRange: 50,
    maxAttempts: 25
  },
  hard: {
    mosquitoSpeed: 3,
    brightness: 0.1,
    hitRange: 40,
    maxAttempts: 20
  },
  expert: {
    mosquitoSpeed: 4,
    brightness: 0.05,
    hitRange: 30,
    maxAttempts: 15
  }
};

// 音效配置
const SOUND_CONFIGS = {
  mosquito: {
    src: '/audio/mosquito.mp3',
    volume: 0.7,
    loop: true
  },
  hit: {
    src: '/audio/hit.mp3',
    volume: 0.8,
    loop: false
  },
  miss: {
    src: '/audio/miss.mp3',
    volume: 0.6,
    loop: false
  },
  levelComplete: {
    src: '/audio/level_complete.mp3',
    volume: 0.9,
    loop: false
  },
  gameOver: {
    src: '/audio/game_over.mp3',
    volume: 0.8,
    loop: false
  }
};

module.exports = {
  calculateDistance,
  calculate3DDistance,
  generateRandomPosition,
  clamp,
  lerp,
  easing,
  degToRad,
  radToDeg,
  generateUUID,
  debounce,
  throttle,
  formatTime,
  formatScore,
  calculateHitRate,
  DIFFICULTY_CONFIGS,
  SOUND_CONFIGS
};
