// utils/audio.js
// 音频工具类

class AudioManager {
  constructor() {
    this.audioContexts = new Map();
    this.isEnabled = true;
  }

  // 创建音频上下文
  createAudio(key, src, options = {}) {
    const audio = wx.createInnerAudioContext();
    audio.src = src;
    audio.autoplay = options.autoplay || false;
    audio.loop = options.loop || false;
    audio.volume = options.volume || 1.0;

    // 添加错误处理
    audio.onError((err) => {
      console.error(`音频 ${key} 播放错误:`, err);
    });

    this.audioContexts.set(key, audio);
    return audio;
  }

  // 播放音频
  play(key, options = {}) {
    if (!this.isEnabled) return;

    const audio = this.audioContexts.get(key);
    if (audio) {
      if (options.volume !== undefined) {
        audio.volume = options.volume;
      }
      if (options.loop !== undefined) {
        audio.loop = options.loop;
      }
      audio.play();
    }
  }

  // 暂停音频
  pause(key) {
    const audio = this.audioContexts.get(key);
    if (audio) {
      audio.pause();
    }
  }

  // 停止音频
  stop(key) {
    const audio = this.audioContexts.get(key);
    if (audio) {
      audio.stop();
    }
  }

  // 设置音量
  setVolume(key, volume) {
    const audio = this.audioContexts.get(key);
    if (audio) {
      audio.volume = Math.max(0, Math.min(1, volume));
    }
  }

  // 设置循环
  setLoop(key, loop) {
    const audio = this.audioContexts.get(key);
    if (audio) {
      audio.loop = loop;
    }
  }

  // 启用/禁用音频
  setEnabled(enabled) {
    this.isEnabled = enabled;
    if (!enabled) {
      // 暂停所有音频
      this.audioContexts.forEach((audio) => {
        audio.pause();
      });
    }
  }

  // 销毁音频上下文
  destroy(key) {
    const audio = this.audioContexts.get(key);
    if (audio) {
      audio.destroy();
      this.audioContexts.delete(key);
    }
  }

  // 销毁所有音频上下文
  destroyAll() {
    this.audioContexts.forEach((audio) => {
      audio.destroy();
    });
    this.audioContexts.clear();
  }

  // 计算3D音频效果
  calculate3DAudio(listenerPos, sourcePos, maxDistance = 1000) {
    const distance = Math.sqrt(
      Math.pow(sourcePos.x - listenerPos.x, 2) +
      Math.pow(sourcePos.y - listenerPos.y, 2) +
      Math.pow(sourcePos.z - listenerPos.z, 2)
    );

    // 计算音量（距离越远音量越小）
    const volume = Math.max(0, 1 - (distance / maxDistance));

    // 计算左右声道平衡
    const deltaX = sourcePos.x - listenerPos.x;
    const balance = Math.max(-1, Math.min(1, deltaX / (maxDistance / 2)));

    return {
      volume: volume,
      balance: balance,
      distance: distance
    };
  }
}

// 创建全局音频管理器实例
const audioManager = new AudioManager();

module.exports = {
  AudioManager,
  audioManager
};
