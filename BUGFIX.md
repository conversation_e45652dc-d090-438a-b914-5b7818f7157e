# Bug 修复记录

## 问题描述
WXML 编译错误：在模板中使用了 JavaScript 方法 `.toFixed()`

### 错误信息
```
Bad value with message: unexpected token `.`.
<text class="distance-text">距离: {{(pressDistance * 100).toFixed(0)}}%</text>
```

## 问题原因
微信小程序的 WXML 模板不支持直接调用 JavaScript 方法，只能使用简单的表达式。

## 解决方案

### 1. 修复距离显示
**问题代码：**
```xml
<text class="distance-text">距离: {{(pressDistance * 100).toFixed(0)}}%</text>
<view class="distance-fill" style="width: {{pressDistance * 100}}%;"></view>
```

**修复后：**
```xml
<text class="distance-text">距离: {{pressDistancePercent}}%</text>
<view class="distance-fill" style="width: {{pressDistancePercent}}%;"></view>
```

**JavaScript 中添加：**
```javascript
data: {
  pressDistancePercent: 0, // 当前长按距离百分比
}

// 在长按动画中计算
pressDistancePercent: Math.round(distance * 100)
```

### 2. 修复血量条显示
**问题代码：**
```xml
<view class="health-fill" style="width: {{(currentHealth / maxHealth) * 100}}%;"></view>
```

**修复后：**
```xml
<view class="health-fill" style="width: {{healthPercent}}%;"></view>
```

**JavaScript 中添加：**
```javascript
data: {
  healthPercent: 100, // 血量百分比
}

// 在扣血时更新
const healthPercent = Math.round((newHealth / this.data.maxHealth) * 100);
this.setData({
  currentHealth: newHealth,
  healthPercent: healthPercent
});
```

## 修复文件列表
- `pages/game/game.wxml` - 修复模板表达式
- `pages/game/game.js` - 添加预计算的百分比数据

## 测试验证
1. ✅ WXML 编译通过
2. ✅ 距离指示器正常显示
3. ✅ 血量条正常显示
4. ✅ 无语法错误

## 最佳实践
在微信小程序 WXML 中：
- ✅ 使用简单的数学运算：`{{value * 100}}`
- ✅ 使用预计算的数据：`{{preCalculatedValue}}`
- ❌ 避免调用方法：`{{value.toFixed(0)}}`
- ❌ 避免复杂表达式：`{{(a / b * 100).toFixed(2)}}`

## 相关文档
- [微信小程序 WXML 语法参考](https://developers.weixin.qq.com/miniprogram/dev/reference/wxml/)
- [数据绑定](https://developers.weixin.qq.com/miniprogram/dev/reference/wxml/data.html)
